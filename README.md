# DIU FabLab Attendance System

This project is a comprehensive attendance tracking system for the Fabrication Laboratory at Daffodil International University. It consists of a Next.js web application and an Arduino-based RFID attendance scanner.

## Features

- Modern web interface for the FabLab homepage
- Attendance tracking system using RFID cards
- Real-time attendance recording via API
- Date-based attendance viewing
- Integration with Arduino ESP8266 and RFID scanner
- Firebase Firestore database for storing users and attendance records

## Project Structure

- `/app` - Next.js application
  - `/api` - REST API endpoints
  - `/attendance` - Attendance viewing page
  - `/components` - Reusable UI components
  - `/lib` - Utility functions and configurations
  - `/types` - TypeScript type definitions
- `/arduino` - Arduino code for the RFID scanner
- `/public` - Static assets
- `/scripts` - Utility scripts (database seeding, etc.)

## Setup Instructions

### Web Application

1. Install dependencies:
   ```bash
   npm install
   npm install firebase firebase-admin
   ```

2. Set up Firebase:
   - Create a Firebase project in the [Firebase Console](https://console.firebase.google.com/)
   - Enable Firestore database
   - Generate a new Service Account key from Project Settings > Service Accounts
   - Save the JSON file securely
   - Create a `.env.local` file in the root of the project with the following variables:
     ```
     FIREBASE_PROJECT_ID=your-project-id
     FIREBASE_CLIENT_EMAIL=<EMAIL>
     FIREBASE_PRIVATE_KEY="your-private-key"
     API_TOKEN=fablab_diu_secret_token_2024
     ```

   > **IMPORTANT**: For the Firebase Admin SDK credentials, you need to:
   > 1. Go to Firebase Console → Project Settings → Service Accounts
   > 2. Click "Generate New Private Key"
   > 3. Use the values from the downloaded JSON file for your environment variables
   > 4. Make sure to include the quotation marks and newline characters in FIREBASE_PRIVATE_KEY
   > 
   > See the [detailed Firebase setup guide](docs/FIREBASE_SETUP.md) for more information and troubleshooting.

3. Seed the database (optional):
   ```bash
   npx ts-node scripts/seed-database.ts
   ```

4. Run the development server:
   ```bash
   npm run dev
   ```

### Arduino RFID Scanner

1. Update the Arduino code in `/arduino/fablab_attendance.ino` with your:
   - WiFi credentials
   - API URL (your Next.js server address)
   - API token (matching the one in your `.env.local` file)

2. Flash the code to your ESP8266 with the Arduino IDE

## API Endpoints

- `POST /api/attendance` - Record attendance with an RFID card
- `GET /api/users` - Get all users
- `POST /api/admin/user` - Create or update a user and RFID mapping
- `DELETE /api/admin/user` - Delete a user and associated RFID mappings
- `GET /api/reports/attendance` - Generate attendance reports for a date range

## Security Considerations

- The API is protected with a token-based authentication
- For production, use a more secure authentication method and HTTPS
- Store sensitive environment variables securely

## API Documentation

### Record Attendance

**Endpoint:** `POST /api/attendance`

**Request:**
```json
{
  "userId": "RFID_CARD_UID",
  "token": "fablab_diu_secret_token_2024"
}
```

**Responses:**

Success (200 OK):
```json
{
  "success": true,
  "message": "Attendance recorded successfully",
  "data": {
    "userId": "RFID_CARD_UID",
    "name": "Student Name",
    "id": "Student ID",
    "date": "DD-MM-YYYY",
    "time": "HH:MM"
  }
}
```

Inactive Account (403 Forbidden):
```json
{
  "success": false,
  "message": "Account inactive",
  "data": {
    "userId": "RFID_CARD_UID",
    "name": "Student Name",
    "id": "Student ID"
  }
}
```

### Fetch Users

**Endpoint:** `GET /api/users`

**Response:**
```json
{
  "success": true,
  "message": "Users fetched successfully",
  "data": [/* array of user objects */]
}
```

## Production Deployment

For production deployment, it's recommended to:

1. Use a proper database like Firebase for user data storage
2. Set API tokens as environment variables
3. Implement proper authentication for the web interface
4. Deploy on Vercel, AWS, or other cloud hosting services

## License

Copyright © 2024 Daffodil International University

# FabLab DIU Frontend

This is the frontend for the FabLab DIU (Daffodil International University) website.

## Features

- Attendance tracking
- User management via admin panel
- Public information about FabLab DIU

## Admin Access

The admin panel is accessible at `/admin` and requires authentication with Firebase. Only authorized administrators can access the panel.

### Admin Credentials

For development purposes, you can use the following credentials:

- Email: <EMAIL>
- Password: admin123

**Note:** In a production environment, you should change these credentials and use a more secure authentication method.

### Admin Functionality

The admin panel allows authorized users to:

1. Filter users by status (active/inactive/all)
2. Edit user information:
   - Name
   - Position
   - Student ID
   - Status (active/inactive)

## Development

To run the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.
