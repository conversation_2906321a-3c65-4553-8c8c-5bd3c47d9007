'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/app/lib/auth-context';
import ProtectedRoute from '@/app/components/ProtectedRoute';
import UserEditModal from '@/app/components/UserEditModal';
import ConfirmationDialog from '@/app/components/ConfirmationDialog';
import { User } from '@/app/types';
import { db } from '@/app/lib/firebase';
import { collection, getDocs, query, where } from 'firebase/firestore';

export default function AdminPage() {
  const { logout } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [statusFilter, setStatusFilter] = useState<boolean | null>(false); // Default to inactive users
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  
  useEffect(() => {
    fetchUsers();
  }, [statusFilter]);
  
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const usersCollection = collection(db, 'users');
      let userQuery;
      
      if (statusFilter !== null) {
        userQuery = query(usersCollection, where("status", "==", statusFilter));
      } else {
        userQuery = usersCollection;
      }
      
      const snapshot = await getDocs(userQuery);
      const userData: User[] = [];
      
      snapshot.forEach((doc) => {
        userData.push({ id: doc.id, ...doc.data() } as User);
      });
      
      setUsers(userData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch users';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };
  
  const handleLogout = async () => {
    try {
      await logout();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to log out';
      setError(errorMessage);
    }
  };
  
  const handleEditUser = (user: User) => {
    setSelectedUser(user);
  };
  
  const handleCloseModal = () => {
    setSelectedUser(null);
  };
  
  const handleSaveUser = () => {
    setSelectedUser(null);
    fetchUsers();
  };

  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
  };

  const confirmDeleteUser = async () => {
    if (!userToDelete) return;
    
    setDeleteLoading(true);
    try {
      // Use the API endpoint to delete the user
      const apiToken = process.env.NEXT_PUBLIC_API_TOKEN || "fablab_diu_secret_token_2024";
      const response = await fetch(`/api/admin/user?userId=${userToDelete.id}&token=${apiToken}`, {
        method: 'DELETE',
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Remove the user from the list
        setUsers(users.filter(u => u.id !== userToDelete.id));
        setUserToDelete(null);
      } else {
        setError(`Failed to delete user: ${data.message}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete user';
      setError(errorMessage);
    } finally {
      setDeleteLoading(false);
      setUserToDelete(null);
    }
  };

  const cancelDeleteUser = () => {
    setUserToDelete(null);
  };
  
  return (
    <ProtectedRoute>
      <div className="p-4 sm:p-6 max-w-6xl mx-auto">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 sm:mb-8 gap-4 sm:gap-0">
          <h1 className="text-2xl sm:text-3xl font-bold text-white">User Management</h1>
          <button
            onClick={handleLogout}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
          >
            Logout
          </button>
        </div>
        
        {error && (
          <div className="mb-6 p-4 bg-red-800 text-white rounded-md border border-red-600 text-sm">
            {error}
            <button 
              className="ml-2 text-white underline hover:text-red-200" 
              onClick={() => setError('')}
            >
              Dismiss
            </button>
          </div>
        )}
        
        <div className="mb-6 flex flex-wrap gap-2">
          <button
            onClick={() => setStatusFilter(false)}
            className={`px-4 py-2 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50 ${
              statusFilter === false
                ? 'bg-blue-600 text-white focus:ring-blue-500'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600 focus:ring-gray-500'
            }`}
          >
            Inactive Users
          </button>
          <button
            onClick={() => setStatusFilter(true)}
            className={`px-4 py-2 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50 ${
              statusFilter === true
                ? 'bg-blue-600 text-white focus:ring-blue-500'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600 focus:ring-gray-500'
            }`}
          >
            Active Users
          </button>
          <button
            onClick={() => setStatusFilter(null)}
            className={`px-4 py-2 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50 ${
              statusFilter === null
                ? 'bg-blue-600 text-white focus:ring-blue-500'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600 focus:ring-gray-500'
            }`}
          >
            All Users
          </button>
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : users.length === 0 ? (
          <div className="text-center p-6 sm:p-8 bg-gray-800 rounded-md border border-gray-700 text-white">
            No users found.
          </div>
        ) : (
          <div className="bg-gray-800 shadow-lg rounded-lg overflow-hidden border border-gray-700">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-700">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Card ID
                    </th>
                    <th className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider hidden sm:table-cell">
                      Position
                    </th>
                    <th className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider hidden md:table-cell">
                      Student ID
                    </th>
                    <th className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-4 sm:px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-gray-800 divide-y divide-gray-700">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-700 transition-colors duration-150">
                      <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-white">{user.name}</div>
                        <div className="text-xs text-gray-300 mt-1 sm:hidden">{user.position}</div>
                      </td>
                      <td className="px-4 sm:px-6 py-4 whitespace-nowrap hidden sm:table-cell">
                        <div className="text-sm text-gray-300">{user.id}</div>
                      </td>
                      <td className="px-4 sm:px-6 py-4 whitespace-nowrap hidden sm:table-cell">
                        <div className="text-sm text-gray-300">{user.position}</div>
                      </td>
                      <td className="px-4 sm:px-6 py-4 whitespace-nowrap hidden md:table-cell">
                        <div className="text-sm text-gray-300">{user.studentId}</div>
                      </td>
                      <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            user.status
                              ? 'bg-green-900 text-green-200'
                              : 'bg-red-900 text-red-200'
                          }`}
                        >
                          {user.status ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-4 sm:px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-3">
                          <button
                            onClick={() => handleEditUser(user)}
                            className="text-blue-400 hover:text-blue-300 focus:outline-none focus:underline"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteUser(user)}
                            className="text-red-400 hover:text-red-300 focus:outline-none focus:underline"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
        
        {userToDelete && (
          <ConfirmationDialog
            isOpen={!!userToDelete}
            title="Delete User"
            message={`Are you sure you want to delete ${userToDelete.name}? This action cannot be undone.`}
            confirmText={deleteLoading ? "Deleting..." : "Delete"}
            cancelText="Cancel"
            onConfirm={confirmDeleteUser}
            onCancel={cancelDeleteUser}
          />
        )}
      </div>
      
      {selectedUser && (
        <UserEditModal
          user={selectedUser}
          onClose={handleCloseModal}
          onSave={handleSaveUser}
        />
      )}
    </ProtectedRoute>
  );
} 