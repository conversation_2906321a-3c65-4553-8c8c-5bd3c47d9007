import { NextRequest, NextResponse } from "next/server";
import { User } from "@/app/types";
import { db } from "@/app/lib/firebase";
import { 
  collection, 
  doc, 
  getDoc, 
  setDoc,
  deleteDoc
} from 'firebase/firestore';

// API token to secure the endpoints
const API_TOKEN = process.env.API_TOKEN || "fablab_diu_secret_token_2024";

// Collections references
const usersCollection = collection(db, 'users');

// CREATE or UPDATE a user
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const { user, token } = data;

    // Validate token
    if (!token || token !== API_TOKEN) {
      return NextResponse.json(
        {
          success: false,
          message: "Unauthorized: Invalid token",
        },
        { status: 401 }
      );
    }

    // Validate required fields
    if (!user || !user.id || !user.name || user.status === undefined || !user.position) {
      return NextResponse.json(
        {
          success: false,
          message: "Bad request: User is required with id, name, status, and position",
        },
        { status: 400 }
      );
    }

    // Initialize entries if not provided
    if (!user.entries) {
      user.entries = {};
    }

    // Create or update the user document
    const userDocRef = doc(usersCollection, user.id);
    await setDoc(userDocRef, user as User, { merge: true });

    return NextResponse.json({
      success: true,
      message: "User created/updated successfully",
      data: {
        user
      },
    });
  } catch (error) {
    console.error("Error creating/updating user:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

// DELETE a user
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get("userId");
    const token = url.searchParams.get("token");

    // Validate token
    if (!token || token !== API_TOKEN) {
      return NextResponse.json(
        {
          success: false,
          message: "Unauthorized: Invalid token",
        },
        { status: 401 }
      );
    }

    // Validate userId
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          message: "Bad request: User ID is required",
        },
        { status: 400 }
      );
    }

    // Check if user exists
    const userDocRef = doc(usersCollection, userId);
    const userDocSnap = await getDoc(userDocRef);
    if (!userDocSnap.exists()) {
      return NextResponse.json(
        {
          success: false,
          message: "User not found",
        },
        { status: 404 }
      );
    }

    // Delete the user
    await deleteDoc(userDocRef);

    return NextResponse.json({
      success: true,
      message: "User deleted successfully",
      data: {
        userId
      },
    });
  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
} 