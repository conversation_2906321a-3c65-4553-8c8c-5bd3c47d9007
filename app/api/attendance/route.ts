import { NextRequest, NextResponse } from "next/server";
import { User } from "@/app/types";
import { db } from "@/app/lib/firebase";
import { collection, doc, getDoc, updateDoc, setDoc } from 'firebase/firestore';

export const dynamic = 'force-dynamic';

const API_TOKEN = process.env.API_TOKEN || "fablab_diu_secret_token_2024";
const usersCollection = collection(db, 'users');

// Set your timezone offset in hours (e.g., +6 for Bangladesh)
const TIMEZONE_OFFSET = 6; // Change this value to match your timezone

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const { userId, token } = data;

    if (!token || token !== API_TOKEN) {
      return NextResponse.json({ success: false, message: "Unauthorized: Invalid token" }, { status: 401 });
    }

    if (!userId) {
      return NextResponse.json({ success: false, message: "Bad request: User ID is required" }, { status: 400 });
    }

    // Use card ID directly as the user document ID
    const userDocRef = doc(usersCollection, userId);
    const userDocSnap = await getDoc(userDocRef);

    if (!userDocSnap.exists()) {
      // Create a new user with inactive status
      const newUser: User = {
        id: userId,
        name: "New User",
        status: false,
        position: "Unknown",
        studentId: "Unknown",
        entries: {},
      };

      await setDoc(userDocRef, newUser);

      return NextResponse.json({
        success: false,
        message: "New user created with inactive status",
        data: {
          userId,
          id: newUser.id,
          name: newUser.name,
          status: newUser.status,
        },
      }, { status: 201 });
    }

    const user: User = userDocSnap.data() as User;

    if (!user.status) {
      return NextResponse.json({
        success: false,
        message: "Account inactive",
        data: { userId, name: user.name, id: user.id },
      }, { status: 403 });
    }

    // Get the UTC time
    const utcNow = new Date();
    
    // Apply timezone offset to get local time
    const localNow = new Date(utcNow.getTime() + (TIMEZONE_OFFSET * 60 * 60 * 1000));
    
    const day = String(localNow.getDate()).padStart(2, "0");
    const month = String(localNow.getMonth() + 1).padStart(2, "0");
    const year = localNow.getFullYear();
    const dateKey = `${day}-${month}-${year}`;
    const hours = String(localNow.getHours()).padStart(2, "0");
    const minutes = String(localNow.getMinutes()).padStart(2, "0");
    const timeString = `${hours}:${minutes}`;

    if (!user.entries) user.entries = {};
    if (!user.entries[dateKey]) user.entries[dateKey] = [];

    user.entries[dateKey].push(timeString);

    await updateDoc(userDocRef, {
      [`entries.${dateKey}`]: user.entries[dateKey]
    });

    return NextResponse.json({
      success: true,
      message: "Attendance recorded successfully",
      data: {
        userId,
        name: user.name,
        id: user.id,
        date: dateKey,
        time: timeString,
      },
    });

  } catch (error) {
    console.error("Error recording attendance:", error);
    return NextResponse.json({ success: false, message: "Internal server error" }, { status: 500 });
  }
}
