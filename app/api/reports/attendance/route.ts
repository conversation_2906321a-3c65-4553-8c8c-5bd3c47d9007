import { NextRequest, NextResponse } from "next/server";
import { User } from "@/app/types";
import { db } from "@/app/lib/firebase";
import { collection, getDocs } from 'firebase/firestore';

// API token to secure the endpoints
const API_TOKEN = process.env.API_TOKEN || "fablab_diu_secret_token_2024";

// Users collection reference
const usersCollection = collection(db, 'users');

// Set your timezone offset in hours (e.g., +6 for Bangladesh)
const TIMEZONE_OFFSET = 6; // Change this value to match your timezone

// GET attendance data for a date range
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const startDate = url.searchParams.get("startDate"); // Format: DD-MM-YYYY
    const endDate = url.searchParams.get("endDate");     // Format: DD-MM-YYYY
    const token = url.searchParams.get("token");

    // Validate token
    if (!token || token !== API_TOKEN) {
      return NextResponse.json(
        {
          success: false,
          message: "Unauthorized: Invalid token",
        },
        { status: 401 }
      );
    }

    // Validate date parameters
    if (!startDate || !endDate) {
      return NextResponse.json(
        {
          success: false,
          message: "Bad request: Start date and end date are required",
        },
        { status: 400 }
      );
    }

    // Validate date format (simple check for DD-MM-YYYY format)
    const dateRegex = /^\d{2}-\d{2}-\d{4}$/;
    if (!dateRegex.test(startDate) || !dateRegex.test(endDate)) {
      return NextResponse.json(
        {
          success: false,
          message: "Bad request: Dates must be in DD-MM-YYYY format",
        },
        { status: 400 }
      );
    }

    // Fetch all users
    const usersSnapshot = await getDocs(usersCollection);
    
    // Process users to extract attendance data for the specified date range
    const attendanceReport = usersSnapshot.docs.map((doc) => {
      const user = doc.data() as User;
      
      // Filter entries to include only those in the date range
      const entriesInRange: Record<string, string[]> = {};
      
      // For each date in user's entries
      Object.keys(user.entries || {}).forEach(dateKey => {
        // Check if the date is within the range
        if (isDateInRange(dateKey, startDate as string, endDate as string)) {
          entriesInRange[dateKey] = user.entries[dateKey];
        }
      });
      
      return {
        id: user.id,
        name: user.name,
        position: user.position || "Unknown",
        status: user.status,
        entries: entriesInRange
      };
    });

    // Add timezone information to the response
    return NextResponse.json({
      success: true,
      message: "Attendance report generated successfully",
      data: {
        startDate,
        endDate,
        timezone: `UTC${TIMEZONE_OFFSET >= 0 ? '+' : ''}${TIMEZONE_OFFSET}`,
        attendanceReport
      },
    });
  } catch (error) {
    console.error("Error generating attendance report:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

// Helper function to check if a date is within a range (inclusive)
function isDateInRange(dateToCheck: string, startDate: string, endDate: string): boolean {
  // Parse dates (DD-MM-YYYY format)
  const [dayCheck, monthCheck, yearCheck] = dateToCheck.split('-').map(Number);
  const [dayStart, monthStart, yearStart] = startDate.split('-').map(Number);
  const [dayEnd, monthEnd, yearEnd] = endDate.split('-').map(Number);
  
  // Create Date objects with consistent timezone handling (months are 0-indexed in JavaScript Date)
  // Use noon (12:00) to avoid any potential daylight saving time issues
  const dateCheck = new Date(Date.UTC(yearCheck, monthCheck - 1, dayCheck, 12, 0, 0));
  const dateStart = new Date(Date.UTC(yearStart, monthStart - 1, dayStart, 12, 0, 0));
  const dateEnd = new Date(Date.UTC(yearEnd, monthEnd - 1, dayEnd, 12, 0, 0));
  
  // Check if the date is within range (inclusive)
  return dateCheck >= dateStart && dateCheck <= dateEnd;
} 