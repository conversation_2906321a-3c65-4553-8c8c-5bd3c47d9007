import { NextResponse } from "next/server";
import { User } from "@/app/types";
import { db } from "@/app/lib/firebase";
import { collection, getDocs } from 'firebase/firestore';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

// Users collection reference
const usersCollection = collection(db, 'users');

export async function GET() {
  try {
    // Fetch all users from Firestore
    const usersSnapshot = await getDocs(usersCollection);
    
    // If no users found
    if (usersSnapshot.empty) {
      return NextResponse.json({
        success: true,
        message: "No users found",
        data: [],
      });
    }
    
    // Map the documents to User objects
    const users: User[] = usersSnapshot.docs.map((doc) => {
      const userData = doc.data() as User;
      return userData;
    });
    
    return NextResponse.json({
      success: true,
      message: "Users fetched successfully",
      data: users,
    });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch users",
      },
      { status: 500 }
    );
  }
} 