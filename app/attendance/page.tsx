"use client";

import { useState, useEffect } from "react";
import { User } from "../types";
import Link from "next/link";
import { db } from "../lib/firebase";
import { collection, getDocs } from 'firebase/firestore';
import UserDetailsPopup from "../components/UserDetailsPopup";

export const dynamic = 'force-dynamic';

export default function AttendancePage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split("T")[0].replace(/-/g, "-")
  );
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedEntries, setSelectedEntries] = useState<string[]>([]);
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const usersCollection = collection(db, 'users');
        const querySnapshot = await getDocs(usersCollection);
        
        const usersData: User[] = [];
        querySnapshot.forEach((doc) => {
          usersData.push({ id: doc.id, ...doc.data() } as User);
        });
        
        setUsers(usersData);
      } catch (err) {
        setError(err instanceof Error ? err.message : "An unknown error occurred");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedDate(e.target.value);
  };

  const getFormattedDateKey = (date: string) => {
    // Convert from YYYY-MM-DD to DD-MM-YYYY format for our storage
    const [year, month, day] = date.split('-');
    return `${day}-${month}-${year}`;
  };

  const handleUserClick = (user: User, entries: string[]) => {
    setSelectedUser(user);
    setSelectedEntries(entries);
    setIsPopupOpen(true);
  };

  const getDisplayTimes = (entries: string[]) => {
    if (entries.length === 0) return [];
    if (entries.length === 1) return [{ time: entries[0], isEntry: true }];
    return [
      { time: entries[0], isEntry: true },
      { time: entries[entries.length - 1], isEntry: entries.length % 2 === 1 }
    ];
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {isPopupOpen && selectedUser && (
        <UserDetailsPopup
          user={selectedUser}
          date={formatDate(selectedDate)}
          entries={selectedEntries}
          onClose={() => setIsPopupOpen(false)}
        />
      )}
      
      {/* Header */}
      <header className="bg-blue-600 dark:bg-blue-800 text-white">
        <div className="max-w-7xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold">FabLab Attendance</h1>
              <p className="mt-1 text-blue-100">Daffodil International University</p>
            </div>
            <Link 
              href="/"
              className="bg-white text-blue-600 hover:bg-blue-50 px-4 py-2 rounded-md text-sm font-medium transition-colors self-start sm:self-auto"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="max-w-7xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 border-b border-gray-200 dark:border-gray-700 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
              Attendance Records
            </h3>
            
            {/* Date selector */}
            <div className="mt-4">
              <label htmlFor="date" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Select Date
              </label>
              <input
                type="date"
                id="date"
                name="date"
                value={selectedDate}
                onChange={handleDateChange}
                className="mt-1 block w-full sm:w-64 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="spinner inline-block w-8 h-8 border-4 border-t-blue-500 border-gray-200 rounded-full animate-spin"></div>
              <p className="mt-4 text-gray-500 dark:text-gray-400">Loading attendance data...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-red-100 dark:bg-red-900">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600 dark:text-red-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <p className="mt-4 text-red-600 dark:text-red-300">{error}</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-900">
                  <tr>
                    <th scope="col" className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Name
                    </th>
                    <th scope="col" className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden sm:table-cell">
                      Position
                    </th>
                    <th scope="col" className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">
                      ID
                    </th>
                    <th scope="col" className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Entries <span className="hidden sm:inline">({formatDate(selectedDate)})</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {users.length === 0 ? (
                    <tr>
                      <td colSpan={5} className="px-4 sm:px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                        No records found for the selected date.
                      </td>
                    </tr>
                  ) : (
                    users.map((user) => {
                      const dateKey = getFormattedDateKey(selectedDate);
                      const entries = user.entries?.[dateKey] || [];
                      
                      return (
                        <tr 
                          key={user.id} 
                          onClick={() => handleUserClick(user, entries)}
                          className="hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                        >
                          <td className="px-4 sm:px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {user.name}
                            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 sm:hidden">{user.position}</div>
                          </td>
                          <td className="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 hidden sm:table-cell">
                            {user.position}
                          </td>
                          <td className="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 hidden md:table-cell">
                            {user.studentId}
                          </td>
                          <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                            {user.status ? (
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                Active
                              </span>
                            ) : (
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                                Inactive
                              </span>
                            )}
                          </td>
                          <td className="px-4 sm:px-6 py-4">
                            {entries.length === 0 ? (
                              <span className="text-gray-400 dark:text-gray-500">No entries</span>
                            ) : (
                              <div className="flex flex-wrap gap-2">
                                {getDisplayTimes(entries).map(({ time, isEntry }, index) => (
                                  <span
                                    key={index}
                                    className={`px-2.5 py-1 inline-flex text-xs leading-4 font-medium rounded-full ${
                                      isEntry
                                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                    }`}
                                  >
                                    {time}
                                  </span>
                                ))}
                                {entries.length > 2 && (
                                  <span className="text-sm text-gray-500 dark:text-gray-400 self-center">
                                    (+{entries.length - 2} more)
                                  </span>
                                )}
                              </div>
                            )}
                          </td>
                        </tr>
                      );
                    })
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </main>
    </div>
  );
} 