"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';

type Link = {
  href: string;
  label: string;
  isButton?: boolean;
  isAdmin?: boolean;
};

export default function MobileMenu({ links }: { links: Link[] }) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Close menu when clicking outside
  useEffect(() => {
    if (isOpen) {
      const handleClick = () => setIsOpen(false);
      document.addEventListener('click', handleClick);
      return () => document.removeEventListener('click', handleClick);
    }
  }, [isOpen]);

  // Prevent scrolling when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <div className="md:hidden">
      <button 
        onClick={(e: React.MouseEvent) => {
          e.stopPropagation();
          setIsOpen(!isOpen);
        }}
        className="relative z-50 p-2 focus:outline-none"
        aria-label="Toggle Menu"
      >
        <div className="w-6 flex flex-col justify-center items-center">
          <span 
            className={`block h-0.5 w-6 bg-gray-800 transition-all duration-300 ease-out ${
              isOpen ? 'rotate-45 translate-y-1' : '-translate-y-0.5'
            }`} 
          />
          <span 
            className={`block h-0.5 w-6 bg-gray-800 transition-all duration-300 ease-out my-0.5 ${
              isOpen ? 'opacity-0' : 'opacity-100'
            }`} 
          />
          <span 
            className={`block h-0.5 w-6 bg-gray-800 transition-all duration-300 ease-out ${
              isOpen ? '-rotate-45 -translate-y-1' : 'translate-y-0.5'
            }`} 
          />
        </div>
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div 
            initial={{ opacity: 0, x: "100%" }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: "100%" }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="fixed inset-0 z-40 bg-white"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            <div className="h-full flex flex-col pt-20 px-8">
              <nav className="flex flex-col space-y-6">
                {links.map((link, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ 
                      delay: i * 0.1,
                      type: "spring",
                      stiffness: 300,
                      damping: 24
                    }}
                  >
                    <Link 
                      href={link.href} 
                      className={`text-2xl font-medium ${
                        link.isButton 
                          ? 'text-blue-600 font-semibold' 
                          : link.isAdmin 
                          ? 'text-gray-500' 
                          : 'text-gray-900'
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      {link.label}
                    </Link>
                  </motion.div>
                ))}
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
} 