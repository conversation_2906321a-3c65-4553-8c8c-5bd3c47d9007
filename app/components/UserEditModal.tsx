'use client';

import { useState, useEffect } from 'react';
import { User } from '@/app/types';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '@/app/lib/firebase';

interface UserEditModalProps {
  user: User | null;
  onClose: () => void;
  onSave: () => void;
}

export default function UserEditModal({ user, onClose, onSave }: UserEditModalProps) {
  const [name, setName] = useState('');
  const [position, setPosition] = useState('');
  const [studentId, setStudentId] = useState('');
  const [status, setStatus] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (user) {
      setName(user.name);
      setPosition(user.position);
      setStudentId(user.studentId);
      setStatus(user.status);
    }
  }, [user]);

  if (!user) return null;

  const handleSave = async () => {
    if (!user.id) return;

    setSaving(true);
    setError('');

    try {
      const userRef = doc(db, 'users', user.id);
      await updateDoc(userRef, {
        name,
        position,
        studentId,
        status,
      });
      onSave();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save changes';
      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 max-w-md w-full shadow-xl">
        <h2 className="text-xl font-bold mb-4 text-white">Edit User</h2>
        
        {error && (
          <div className="mb-4 p-3 bg-red-800 text-white rounded-md text-sm border border-red-600">
            {error}
          </div>
        )}
        
        <div className="mb-4">
          <label className="block text-gray-300 text-sm font-bold mb-2">
            Name
          </label>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full p-2 border rounded-md bg-gray-700 text-white border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-300 text-sm font-bold mb-2">
            Position
          </label>
          <input
            type="text"
            value={position}
            onChange={(e) => setPosition(e.target.value)}
            className="w-full p-2 border rounded-md bg-gray-700 text-white border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-300 text-sm font-bold mb-2">
            Student ID
          </label>
          <input
            type="text"
            value={studentId}
            onChange={(e) => setStudentId(e.target.value)}
            className="w-full p-2 border rounded-md bg-gray-700 text-white border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <div className="mb-6">
          <label className="flex items-center text-gray-300 text-sm font-bold">
            <input
              type="checkbox"
              checked={status}
              onChange={(e) => setStatus(e.target.checked)}
              className="mr-2 h-4 w-4 accent-blue-600 rounded focus:ring-2 focus:ring-blue-500"
            />
            Active
          </label>
        </div>
        
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={saving}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-200"
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </div>
  );
} 