@import "tailwindcss";
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: var(--background);
  font-family: var(--font-sans, system-ui, -apple-system, BlinkMacSystemFont);
}

/* Details/Summary styling for mobile menu */
summary {
  list-style: none;
}

summary::-webkit-details-marker {
  display: none;
}

details summary {
  cursor: pointer;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

/* Animation utility classes */
.animate-fade-in {
  animation: fadeIn 0.8s ease forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 1s ease forwards;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease forwards;
}

.animate-float {
  animation: float 5s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s infinite;
}

.animate-spin-slow {
  animation: spin-slow 30s linear infinite;
}

.animate-marquee {
  animation: marquee 25s linear infinite;
}

/* Animation delay utilities */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

.delay-1000 {
  animation-delay: 1s;
}

/* Particle animation */
@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.animate-particle-float {
  animation: particle-float 6s ease-in-out infinite;
}

/* Glow effect */
.glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.glow-hover:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.7);
  transition: box-shadow 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}

/* Glass effect */
.glass {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient text */
.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, #3b82f6, #2563eb);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
