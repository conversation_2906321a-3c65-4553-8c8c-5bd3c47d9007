import type { Metadata } from "next";
import { Geist, <PERSON>ei<PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "FabLab DIU | Innovation Through Digital Fabrication",
  description: "Fabrication Lab of Department of Computer Science and Engineering, Daffodil International University",
  keywords: ["fablab", "digital fabrication", "innovation", "3D printing", "DIU", "maker space"],
  viewport: "width=device-width, initial-scale=1, maximum-scale=1",
  themeColor: "#3b82f6",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
