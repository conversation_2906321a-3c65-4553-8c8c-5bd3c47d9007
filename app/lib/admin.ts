import { User as FirebaseUser } from "firebase/auth";

// Array of email addresses allowed to access the admin panel
// In a production app, this would be stored in a database or environment variable
const ADMIN_EMAILS = [
  "<EMAIL>",
];

/**
 * Check if a user is authorized to access the admin panel
 */
export function isAuthorizedAdmin(user: FirebaseUser | null): boolean {
  if (!user) {
    return false;
  }
  
  return ADMIN_EMAILS.includes(user.email?.toLowerCase() || "");
}

/**
 * Function to create an admin user (this would typically be done via Firebase Console)
 * For development purposes only
 */
export function getAdminCredentials() {
  return {
    email: ADMIN_EMAILS[0],
    // This would be a secure password in production
    password: "admin123"
  };
} 