import { initializeApp, getApps } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Initialize Firebase Client SDK
const firebaseConfig = {
    apiKey: "AIzaSyDNE6Gr1WSWxkhcXGHOOzls5dH5BMy7uak",
  
    authDomain: "fablab-attendence.firebaseapp.com",
  
    databaseURL: "https://fablab-attendence-default-rtdb.firebaseio.com",
  
    projectId: "fablab-attendence",
  
    storageBucket: "fablab-attendence.firebasestorage.app",
  
    messagingSenderId: "256866745176",
  
    appId: "1:256866745176:web:a83ad6bdf5470dc2e7c32d",
  
    measurementId: "G-NBPDPZ1HF9"  
};

// Initialize Firebase only if it hasn't been initialized already
const apps = getApps();
const firebaseApp = apps.length 
  ? apps[0] 
  : initializeApp(firebaseConfig);

// Initialize Firestore
const db = getFirestore(firebaseApp);
const auth = getAuth(firebaseApp);

export { db, auth }; 