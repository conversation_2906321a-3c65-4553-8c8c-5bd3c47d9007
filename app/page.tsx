'use client';

import { useState, useEffect } from 'react';
import Image from "next/image";
import Link from "next/link";
import { Montserrat } from 'next/font/google';
import MobileMenu from './components/MobileMenu';
import { motion } from 'framer-motion';

const montserrat = Montserrat({ subsets: ['latin'] });

const navigationLinks = [
  { href: "/", label: "Home" },
  { href: "#about", label: "About" },
  { href: "#facilities", label: "Facilities" },
  { href: "#events", label: "Events" },
  { href: "#team", label: "Team" },
  { href: "/membership", label: "Membership", isButton: true },
  { href: "/admin", label: "Admin", isAdmin: true },
];

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.6, ease: "easeOut" }
  }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: 0.5, ease: "easeOut" }
  }
};

const fadeInUp = {
  hidden: { opacity: 0, y: 40 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

export default function Home() {
  const [scrolled, setScrolled] = useState(false);

  // Handle navbar background on scroll
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className={`min-h-screen bg-white ${montserrat.className}`}>
      {/* Navigation Bar */}
      <motion.nav 
        className={`fixed w-full z-50 py-4 transition-all duration-300 ${
          scrolled ? 'bg-white/95 backdrop-blur-md shadow-md' : 'bg-transparent'
        }`}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ type: 'spring', stiffness: 300, damping: 25 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="h-10 w-auto relative">
                <Image
                  src="/fablab-logo.png"
                  alt="FabLab DIU Logo"
                  width={150}
                  height={40}
                  className="object-contain"
                />
              </div>
            </Link>
            
            <div className="hidden md:flex items-center space-x-8">
              {navigationLinks.slice(0, 5).map((link, i) => (
                <motion.div
                  key={i}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: i * 0.1 }}
                >
                  <Link 
                    href={link.href} 
                    className={`text-gray-800 hover:text-blue-600 transition-colors duration-300 relative after:absolute after:w-0 after:h-0.5 after:bg-blue-600 after:bottom-0 after:left-0 hover:after:w-full after:transition-all after:duration-300`}
                  >
                    {link.label}
                  </Link>
                </motion.div>
              ))}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5, type: "spring" }}
              >
                <Link 
                  href="#membership" 
                  className="px-5 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                >
                  Membership
                </Link>
              </motion.div>
              {/* <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                <Link 
                  href="/admin" 
                  className="text-gray-600 hover:text-blue-600 transition-colors duration-300"
                >
                  Admin
                </Link>
              </motion.div> */}
            </div>
            
            {/* Mobile Menu */}
            <MobileMenu links={navigationLinks} />
          </div>
        </div>
      </motion.nav>

      {/* Hero Section */}
      <div className="relative min-h-screen pt-28 md:pt-36 pb-20 md:pb-28 px-6 bg-gradient-to-br from-white via-blue-50 to-white overflow-hidden">
        {/* Decorative Elements */}
        <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-br from-blue-100/40 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-gradient-to-tr from-blue-100/40 to-transparent rounded-full blur-3xl"></div>
        
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div 
              className="space-y-8 relative"
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={staggerContainer}
            >
              <div className="space-y-5">
                <motion.span 
                  variants={fadeInUp}
                  className="inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium shadow-sm"
                >
                  Welcome to FabLab DIU
                </motion.span>
                <motion.h1 
                  variants={fadeInUp}
                  className="text-5xl md:text-7xl font-bold text-gray-900 leading-tight"
                >
                  Innovation Through
                  <span className="text-gradient bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-800"> Digital Fabrication</span>
                </motion.h1>
                <motion.p 
                  variants={fadeInUp}
                  className="text-xl text-gray-600 leading-relaxed max-w-lg"
                >
                  Learning how to make anything for innovation & industrial use at Daffodil International University.
                </motion.p>
              </div>
              
              <motion.div 
                variants={fadeInUp}
                className="flex flex-wrap gap-4"
              >
                <Link
                  href="#membership"
                  className="px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-full font-medium hover:shadow-lg hover:shadow-blue-500/30 transition-all duration-300 transform hover:scale-105 shadow-md"
                >
                  Join Us Now
                </Link>
                <Link
                  href="#about"
                  className="px-8 py-4 bg-white text-blue-600 border-2 border-blue-100 rounded-full font-medium hover:bg-blue-50 hover:border-blue-200 transition-all duration-300 shadow-sm"
                >
                  Discover More
                </Link>
              </motion.div>

              {/* Stats */}
              <motion.div 
                variants={fadeInUp}
                className="grid grid-cols-3 gap-6 pt-8 border-t border-gray-100"
              >
                {[
                  { value: "500+", label: "Students" },
                  { value: "50+", label: "Projects" },
                  { value: "20+", label: "Workshops" }
                ].map((stat, i) => (
                  <div key={i} className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{stat.value}</div>
                    <div className="text-gray-600 text-sm">{stat.label}</div>
                  </div>
                ))}
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 100 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ type: "spring", duration: 1 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="relative h-[500px] md:h-[600px] rounded-2xl overflow-hidden shadow-2xl transform transition-all duration-500 hover:scale-[1.02] group">
                <Image 
                  src="/fablab-carousel-1.webp" 
                  alt="FabLab DIU" 
                  fill 
                  className="object-cover"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-blue-900/50 to-transparent group-hover:from-blue-900/40 transition-all duration-500"></div>
                
                {/* Floating Cards */}
                <motion.div 
                  className="absolute bottom-8 left-8 right-8 bg-white/90 backdrop-blur-sm p-6 rounded-xl shadow-lg"
                  initial={{ y: 20, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Latest Achievement</h3>
                      <p className="text-sm text-gray-600">Winner of National Innovation Competition 2023</p>
                    </div>
                  </div>
                </motion.div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-100 rounded-full blur-2xl opacity-80"></div>
              <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-blue-50 rounded-full blur-3xl opacity-80"></div>
            </motion.div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <motion.div 
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 10, 0] }}
          transition={{ repeat: Infinity, duration: 2 }}
        >
          <div className="w-6 h-10 border-2 border-blue-600 rounded-full flex items-start justify-center p-2">
            <div className="w-1.5 h-3 bg-blue-600 rounded-full"></div>
          </div>
        </motion.div>
      </div>

      {/* Main Content */}
      <main className="px-6">
        {/* Announcement Bar */}
        <div className="max-w-7xl mx-auto my-10 py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl text-white flex items-center shadow-lg overflow-hidden relative">
          <div className="font-semibold mr-4 shrink-0">ANNOUNCEMENT:</div>
          <div className="overflow-hidden">
            <p className="animate-marquee whitespace-nowrap">
              To ensure learners&apos; engagement in corporate workflows from concept, design, prototyping, fabrication and marketing.
            </p>
          </div>
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-40 h-full bg-gradient-to-l from-blue-600/20 to-transparent"></div>
          <div className="absolute -top-10 -left-10 w-20 h-20 bg-blue-500/20 rounded-full"></div>
        </div>

        {/* About Section */}
        <motion.section 
          id="about" 
          className="max-w-7xl mx-auto py-20"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <motion.div 
              variants={scaleIn}
              className="relative"
            >
              <div className="relative h-[350px] md:h-[400px] rounded-lg overflow-hidden shadow-xl z-10">
                <Image
                  src="/fablab-video-thumb.jpg"
                  alt="FabLab DIU Video"
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gray-900/30 flex items-center justify-center">
                  <button className="w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-lg transform transition-transform hover:scale-110 hover:shadow-xl">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
              {/* Decorative elements */}
              <div className="absolute top-5 -left-5 w-64 h-64 bg-blue-100 rounded-lg -z-10 transform rotate-3"></div>
            </motion.div>
            <motion.div 
              className="space-y-6"
              variants={staggerContainer}
            >
              <motion.div variants={fadeIn}>
                <span className="text-sm font-semibold text-blue-600 tracking-wider uppercase">About Us</span>
                <h2 className="text-3xl md:text-4xl font-bold mt-2 text-gray-900">FabLab DIU</h2>
              </motion.div>
              <motion.p 
                variants={fadeIn}
                className="text-gray-600 leading-relaxed"
              >
                To ensure learners&apos; engagement in corporate workflows from concept, design, prototyping, fabrication and marketing, DIU has set up a Multi-Disciplinary Fabrication Laboratory in 2020 to provide a clear idea of product creation through the application of &ldquo;learning-by-doing&rdquo; and &ldquo;hands-on&rdquo; activities.
              </motion.p>
              <motion.p 
                variants={fadeIn}
                className="text-gray-600 leading-relaxed"
              >
                Moreover, the DIU FabLab aims to develop and implement a set of training programs with some modules, based on the latest modern technologies.
              </motion.p>
              <motion.div variants={fadeIn}>
                <Link
                  href="/attendance"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-md hover:shadow-lg"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                  View Attendance
                </Link>
              </motion.div>
            </motion.div>
          </div>
        </motion.section>

        {/* Services Section with enhanced design */}
        <motion.section 
          className="max-w-7xl mx-auto py-20 relative"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
        >
          <div className="absolute inset-0 bg-gradient-to-b from-blue-50/50 to-transparent rounded-3xl -z-10"></div>
          
          <motion.div 
            className="text-center max-w-3xl mx-auto mb-16"
            variants={staggerContainer}
          >
            <motion.span 
              variants={fadeIn}
              className="inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4"
            >
              What We Offer
            </motion.span>
            <motion.h2 
              variants={fadeIn}
              className="text-4xl md:text-5xl font-bold mt-2 mb-4 text-gray-900"
            >
              Our Services
            </motion.h2>
            <motion.p 
              variants={fadeIn}
              className="text-xl text-gray-600 leading-relaxed"
            >
              We provide a range of services designed to foster innovation, creativity and technical skill development.
            </motion.p>
          </motion.div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[
              {
                title: "Seminar",
                description: "We always arrange Innovative and Informative seminars for our students related to the 4th Industrial Revolution.",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                ),
                color: "blue",
                delay: 0,
              },
              {
                title: "Training",
                description: "We offer hands-on training based on modern technologies like Educational Robotics, IoT, Laser Printing, CNC Milling, and 3D printing.",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                  </svg>
                ),
                color: "red",
                delay: 0.1,
              },
              {
                title: "Instrument",
                description: "We provide state-of-the-art fabrication tools and equipment for students to build and test their innovative ideas.",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                ),
                color: "green",
                delay: 0.2,
              },
              {
                title: "Research",
                description: "Our curriculum helps students develop the skills required for 21st-century employment through research projects.",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                ),
                color: "blue",
                delay: 0.3,
              },
            ].map((service, i) => (
              <motion.div 
                key={i} 
                className={`bg-white rounded-xl p-6 shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-2 group`}
                variants={fadeIn}
                custom={i}
                transition={{ delay: service.delay }}
              >
                <div className={`w-14 h-14 rounded-lg mb-5 flex items-center justify-center bg-${service.color}-100 text-${service.color}-600 group-hover:bg-${service.color}-600 group-hover:text-white transition-all duration-300`}>
                  {service.icon}
                </div>
                <h3 className="text-xl font-bold mb-3 text-gray-900">{service.title}</h3>
                <p className="text-gray-600">{service.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Facilities/Features Section */}
        <motion.section 
          id="facilities" 
          className="max-w-7xl mx-auto py-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
        >
          <div className="text-center max-w-3xl mx-auto mb-16">
            <span className="text-sm font-semibold text-blue-600 tracking-wider uppercase">What We Have</span>
            <h2 className="text-3xl md:text-4xl font-bold mt-2 mb-4 text-gray-900">Our Facilities</h2>
            <p className="text-gray-600 leading-relaxed">
              Explore the various facilities and resources available at our fabrication laboratory.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "3D Printing",
                description: "Access to industrial grade 3D printers with various materials",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2M7 7h10" />
                  </svg>
                ),
              },
              {
                title: "Laser Cutting",
                description: "Precision laser cutting machines for detailed prototyping",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                ),
              },
              {
                title: "IoT Workshops",
                description: "Learn and build Internet of Things projects with expert guidance",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                  </svg>
                ),
              },
            ].map((facility, i) => (
              <div 
                key={i} 
                className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-blue-100 relative overflow-hidden group"
              >
                <div className="absolute top-0 right-0 w-32 h-32 bg-blue-50 rounded-full -mt-10 -mr-10 transition-all duration-500 group-hover:bg-blue-100"></div>
                <div className="relative">
                  <div className="text-blue-600 mb-6">{facility.icon}</div>
                  <h3 className="text-xl font-bold mb-3 text-gray-900">{facility.title}</h3>
                  <p className="text-gray-600">{facility.description}</p>
                </div>
              </div>
            ))}
          </div>
        </motion.section>

        {/* Workshop Facilities Section */}
        <motion.section 
          className="max-w-7xl mx-auto py-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Workshop",
                description: "Workshop on using and introduction to different tools of the laboratory",
                image: "/facility-1.webp",
              },
              {
                title: "Competition",
                description: "Numerous project competitions among students with the best projects awarded and sent to inter-university contests",
                image: "/facility-2.webp",
              },
              {
                title: "Seminar",
                description: "Seminars on different topics about Fabrication and 4th IR",
                image: "/facility-3.webp",
              },
            ].map((facility, i) => (
              <div key={i} className="group">
                <div className="relative h-64 overflow-hidden rounded-xl shadow-md">
                  <Image 
                    src={facility.image} 
                    alt={facility.title}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-80"></div>
                  <div className="absolute bottom-0 left-0 p-6 text-white">
                    <h3 className="text-xl font-bold mb-2">{facility.title}</h3>
                    <p className="text-white/90 text-sm max-w-xs">{facility.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.section>
        
        {/* Recent Events Section */}
        <motion.section 
          id="events" 
          className="max-w-7xl mx-auto py-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
        >
          <div className="text-center max-w-3xl mx-auto mb-16">
            <span className="text-sm font-semibold text-blue-600 tracking-wider uppercase">What&apos;s Happening</span>
            <h2 className="text-3xl md:text-4xl font-bold mt-2 mb-4 text-gray-900">Recent Events</h2>
            <p className="text-gray-600 leading-relaxed">
              Explore our recent events and activities where students showcase their innovations and creativity.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Fab Lab Students Recruiting Booth",
                date: "September 11, 2024",
                description: "A formal student recruiting booth was held.",
                image: "/event-1.webp",
              },
              {
                title: "Product Design and Idea Competition",
                date: "August 30, 2023",
                description: "A collocated event of 2nd International Conference on Big Data, IoT and Machine Learning conference 2023 (BIM-2023).",
                image: "/event-2.webp",
              },
              {
                title: "Digital Crafts Design and Printing by 3D Printers",
                date: "August 27, 2023",
                description: "FabLab DIU conducted a workshop on Digital Crafts Design & Printing by 3D Printer supported by a2i.",
                image: "/event-3.webp",
              },
            ].map((event, i) => (
              <div key={i} className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 transform group hover:-translate-y-1">
                <div className="relative h-56 overflow-hidden">
                  <Image 
                    src={event.image} 
                    alt={event.title}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-105"
                  />
                  <div className="absolute top-4 left-4 bg-blue-600/90 backdrop-blur-sm text-white text-xs py-1.5 px-3 rounded-full">
                    {event.date}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-gray-900">{event.title}</h3>
                  <p className="text-gray-600 mb-5">{event.description}</p>
                  <Link href="#" className="inline-flex items-center text-blue-600 font-medium hover:text-blue-800 transition-colors group">
                    <span>Read More</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </motion.section>

        {/* Team/Committee Section */}
        <motion.section 
          id="team" 
          className="max-w-7xl mx-auto py-16 relative"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
        >
          <div className="absolute top-20 right-0 w-72 h-72 bg-blue-50 rounded-full -z-10 blur-3xl opacity-70"></div>
          
          <div className="text-center max-w-3xl mx-auto mb-16">
            <span className="text-sm font-semibold text-blue-600 tracking-wider uppercase">Who We Are</span>
            <h2 className="text-3xl md:text-4xl font-bold mt-2 mb-4 text-gray-900">Our Team</h2>
            <p className="text-gray-600 leading-relaxed">
              Meet the dedicated team behind FabLab DIU, working together to foster innovation and creativity.
            </p>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-6">
            {[
              {
                name: "Dr. S. M. Aminul Haque",
                role: "Convener",
                title: "Associate Professor",
                department: "CSE",
                image: "/committee-1.webp",
              },
              {
                name: "Engineer Golam Rabbany",
                role: "Lab in-charge",
                title: "Lecturer",
                department: "CSE",
                image: "/committee-2.webp",
              },
              {
                name: "Md. Shamsuzzaman Miah",
                role: "Lab Officer",
                title: "Technical Officer",
                department: "CSE",
                image: "/committee-3.webp",
              },
              {
                name: "Mr. Kazi Jahid Hasan",
                role: "Advisor",
                title: "Lecturer",
                department: "MCT",
                image: "/committee-4.webp",
              },
            ].map((member, i) => (
              <div key={i} className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 group border border-gray-100">
                <div className="relative h-64 overflow-hidden">
                  <Image 
                    src={member.image} 
                    alt={member.name}
                    fill
                    className="object-cover object-top transition-transform duration-700 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-white via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div className="p-5 text-center relative bg-white transform -translate-y-8 rounded-t-2xl">
                  <h3 className="font-bold text-lg text-gray-900 mb-1">{member.name}</h3>
                  <p className="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium mb-2">{member.role}</p>
                  <p className="text-gray-600 text-sm">{member.title}, {member.department}</p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-10">
            <Link href="#" className="inline-flex items-center text-blue-600 font-medium hover:text-blue-800 transition-colors group">
              <span>View All Team Members</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </Link>
          </div>
        </motion.section>

        {/* Partners Section */}
        <motion.section 
          className="max-w-7xl mx-auto py-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
        >
          <div className="text-center max-w-3xl mx-auto mb-16">
            <span className="text-sm font-semibold text-blue-600 tracking-wider uppercase">Collaborations</span>
            <h2 className="text-3xl md:text-4xl font-bold mt-2 mb-4 text-gray-900">Our Partners</h2>
            <p className="text-gray-600 leading-relaxed">
              Collaborating with leading organizations to bring innovation and technology to our students.
            </p>
          </div>
          
          <div className="flex flex-wrap justify-center items-center gap-12 px-6">
            {[
              { name: "ICTD", image: "/partner-1.webp" },
              { name: "BHTPA", image: "/partner-2.webp" },
              { name: "a2i", image: "/partner-3.webp" },
            ].map((partner, i) => (
              <div key={i} className="w-48 h-24 relative grayscale hover:grayscale-0 transition-all duration-500 transform hover:scale-105">
                <Image 
                  src={partner.image} 
                  alt={partner.name}
                  fill
                  className="object-contain"
                />
              </div>
            ))}
          </div>
        </motion.section>

        {/* Gallery Section */}
        <motion.section 
          className="max-w-7xl mx-auto py-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
        >
          <div className="text-center max-w-3xl mx-auto mb-10">
            <span className="text-sm font-semibold text-blue-600 tracking-wider uppercase">Our Activities</span>
            <h2 className="text-3xl md:text-4xl font-bold mt-2 mb-4 text-gray-900">Gallery</h2>
            <p className="text-gray-600 leading-relaxed">
              Explore photos from our events, workshops, and student activities.
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {[1, 2, 3, 4].map((img, i) => (
              <div key={i} className="relative group overflow-hidden rounded-lg shadow-md aspect-square">
                <Image 
                  src={`/gallery-${img}.webp`} 
                  alt={`Gallery image ${img}`}
                  width={400}
                  height={400}
                  className="object-cover w-full h-full transform transition-transform duration-700 ease-in-out group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                  <button className="w-12 h-12 bg-white rounded-full flex items-center justify-center transform scale-0 group-hover:scale-100 transition-transform duration-300 delay-100">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-10">
            <Link href="#" className="inline-flex items-center text-blue-600 font-medium hover:text-blue-800 transition-colors group">
              <span>View All Gallery</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </Link>
          </div>
        </motion.section>

        {/* Enhanced CTA Section */}
        <motion.section 
          id="membership" 
          className="max-w-7xl mx-auto py-20 mb-10"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
        >
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl overflow-hidden shadow-2xl relative">
            <div className="absolute top-0 right-0 w-96 h-96 bg-white/10 rounded-full -mt-20 -mr-20 blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-96 h-96 bg-white/5 rounded-full -mb-20 -ml-20 blur-2xl"></div>
            
            <div className="relative py-16 px-6 md:px-12 text-center text-white z-10">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Join FabLab DIU?</h2>
                <p className="text-xl text-blue-100 max-w-2xl mx-auto mb-10">
                  Become a member of our fabrication laboratory and gain access to state-of-the-art equipment, workshops, and a community of innovators.
                </p>
                <div className="flex flex-wrap justify-center gap-6">
                  <Link
                    href="/membership"
                    className="px-10 py-4 bg-white text-blue-700 rounded-full font-medium hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl"
                  >
                    Apply for Membership
                  </Link>
                  <Link
                    href="#about"
                    className="px-10 py-4 bg-blue-700/30 backdrop-blur-sm text-white border border-white/20 rounded-full font-medium hover:bg-blue-700/50 transition-all duration-300 hover:border-white/40"
                  >
                    Learn More
                  </Link>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.section>
      </main>
      
      {/* Footer */}
      <footer className="bg-gray-900 text-white pt-20 pb-6">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12 mb-16">
            {/* Column 1 */}
            <div className="md:col-span-2">
              <div className="mb-6">
                <Image
                  src="/fablab-logo.png"
                  alt="FabLab DIU Logo"
                  width={150}
                  height={40}
                  className="object-contain filter brightness-0 invert"
                />
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                Learning how to make anything for innovation & industrial use at Daffodil International University. Our fabrication laboratory is equipped with state-of-the-art digital fabrication tools.
              </p>
              <div className="flex space-x-4">
                {['facebook', 'twitter', 'instagram', 'linkedin'].map((social) => (
                  <a 
                    key={social}
                    href="#" 
                    className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center hover:bg-blue-600 transition-colors"
                  >
                    <span className="sr-only">{social}</span>
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
                    </svg>
                  </a>
                ))}
              </div>
            </div>
            
            {/* Column 2 */}
            <div>
              <h3 className="text-lg font-bold mb-6">Quick Links</h3>
              <ul className="space-y-3">
                {['Home', 'About', 'Facilities', 'Events', 'Team', 'Membership'].map((item) => (
                  <li key={item}>
                    <Link href={`#${item.toLowerCase()}`} className="text-gray-400 hover:text-white transition-colors duration-300">
                      {item}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            
            {/* Column 3 */}
            <div>
              <h3 className="text-lg font-bold mb-6">Contact Us</h3>
              <ul className="space-y-3 text-gray-400">
                <li className="flex items-start space-x-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <span>Department of CSE, Daffodil International University, Dhaka, Bangladesh</span>
                </li>
                <li className="flex items-center space-x-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span><EMAIL></span>
                </li>
                <li className="flex items-center space-x-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  <span>+880 1234-567890</span>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 pt-8 mt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-500 text-sm text-center md:text-left">
                &copy; {new Date().getFullYear()} Daffodil International University. All Rights Reserved.
              </p>
              <div className="mt-4 md:mt-0">
                <ul className="flex space-x-4 text-gray-500 text-sm">
                  <li><a href="#" className="hover:text-white transition-colors">Privacy Policy</a></li>
                  <li><a href="#" className="hover:text-white transition-colors">Terms of Service</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
