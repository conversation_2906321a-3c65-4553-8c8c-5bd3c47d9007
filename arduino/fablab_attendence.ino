#include <SPI.h>
#include <MFRC522.h>
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>
#include <ESP8266WiFi.h>
#include <ESP8266HTTPClient.h>
#include <WiFiClient.h>
#include <ArduinoJson.h>
#include <WiFiClientSecure.h>

#define BUZZER_PIN D0  // or any other digital pin

// Display configuration
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_RESET    -1
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);

// RFID configuration
#define SS_PIN  D8
#define RST_PIN D4
MFRC522 mfrc522(SS_PIN, RST_PIN);

// WiFi configuration
const char* ssid = "SHARIF NET";
const char* password = "srur2003";

// API configuration
const char* apiUrl = "https://fablab.dubd.site/api/attendance";
const char* apiToken = "fablab_diu_secret_token_2024";

// System state
bool cardPresent = false;
unsigned long lastWiFiRetry = 0;
const int wifiRetryInterval = 5000;

// Animation frames for loading animation
const unsigned char PROGMEM wifiIcon[] = {
  0x00, 0x0F, 0x80, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x7C, 0x78, 0x00, 0x00, 0xF0, 0x1E, 0x00,
  0x01, 0xE0, 0x0F, 0x00, 0x03, 0xC0, 0x07, 0x80, 0x07, 0x80, 0x03, 0xC0, 0x07, 0x00, 0x01, 0xC0,
  0x0F, 0x00, 0x01, 0xE0, 0x0E, 0x00, 0x00, 0xE0, 0x0E, 0x00, 0x00, 0xE0, 0x0E, 0x00, 0x00, 0xE0
};

const unsigned char PROGMEM cardIcon[] = {
  0x00, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xFC, 0x40, 0x00, 0x00, 0x02, 0x40, 0x00, 0x00, 0x02,
  0x40, 0x7F, 0xFE, 0x02, 0x40, 0x40, 0x02, 0x02, 0x40, 0x40, 0x02, 0x02, 0x40, 0x40, 0x02, 0x02,
  0x40, 0x40, 0x02, 0x02, 0x40, 0x40, 0x02, 0x02, 0x40, 0x40, 0x02, 0x02, 0x40, 0x40, 0x02, 0x02,
  0x40, 0x7F, 0xFE, 0x02, 0x40, 0x00, 0x00, 0x02, 0x40, 0x00, 0x00, 0x02, 0x3F, 0xFF, 0xFF, 0xFC
};

const unsigned char PROGMEM checkIcon[] = {
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xE0,
  0x00, 0x00, 0x01, 0xC0, 0x00, 0x00, 0x03, 0x80, 0x20, 0x00, 0x07, 0x00, 0x30, 0x00, 0x0E, 0x00,
  0x18, 0x00, 0x1C, 0x00, 0x0C, 0x00, 0x38, 0x00, 0x06, 0x00, 0x70, 0x00, 0x03, 0x00, 0xE0, 0x00,
  0x01, 0x81, 0xC0, 0x00, 0x00, 0xC3, 0x80, 0x00, 0x00, 0x67, 0x00, 0x00, 0x00, 0x3E, 0x00, 0x00
};

const unsigned char PROGMEM errorIcon[] = {
  0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x03, 0xFF, 0xC0, 0x00, 0x0F, 0xFF, 0xF0, 0x00,
  0x1F, 0x00, 0xF8, 0x00, 0x3C, 0x00, 0x3C, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x70, 0x00, 0x0E, 0x00,
  0xE0, 0x00, 0x07, 0x00, 0xE0, 0x00, 0x07, 0x00, 0xE0, 0x00, 0x07, 0x00, 0xE0, 0x00, 0x07, 0x00,
  0xE0, 0x00, 0x07, 0x00, 0x70, 0x00, 0x0E, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x3C, 0x00, 0x3C, 0x00,
  0x1F, 0x00, 0xF8, 0x00, 0x0F, 0xFF, 0xF0, 0x00, 0x03, 0xFF, 0xC0, 0x00, 0x00, 0x7E, 0x00, 0x00
};

// Animation states
int animationFrame = 0;
unsigned long lastAnimationUpdate = 0;
const int animationSpeed = 100; // ms between frames

void setup() {
  pinMode(BUZZER_PIN, OUTPUT);
  Serial.begin(9600);
  
  // Init OLED
  if(!display.begin(SSD1306_SWITCHCAPVCC, 0x3C)) {
    Serial.println(F("OLED not found"));
    while (true);
  }
  
  display.clearDisplay();
  display.setTextColor(SSD1306_WHITE);
  
  // Splash screen animation
  showSplashScreen();
  
  // Init RFID
  SPI.begin();
  mfrc522.PCD_Init();
  
  // Connect to WiFi
  connectToWiFi();
  
  Serial.println("Ready. Scan RFID tag.");
}

void loop() {
  // Check WiFi connection
  if (WiFi.status() != WL_CONNECTED) {
    showWiFiConnectingAnimation();
    
    unsigned long currentMillis = millis();
    if (currentMillis - lastWiFiRetry >= wifiRetryInterval) {
      lastWiFiRetry = currentMillis;
      connectToWiFi();
    }
    return;
  }
  
  // Show ready message with subtle animation
  if (!cardPresent) {
    showReadyScreen();
  }
  
  // Check for new cards
  if (!mfrc522.PICC_IsNewCardPresent() || !mfrc522.PICC_ReadCardSerial()) {
    cardPresent = false;
    return;
  }
  
  // Avoid re-processing card
  if (cardPresent) return;
  cardPresent = true;
  
  // Get UID
  String cardUid = getCardUID();
  
  // Show processing animation
  showCardProcessingAnimation();

  // Beep feedback
  beepPattern(1);
  
  // Send to API
  sendAttendanceData(cardUid);
  
  delay(3000);
  cardPresent = false;
}

void showSplashScreen() {
  for (int i = 0; i < 64; i += 4) {
    display.clearDisplay();
    
    // Draw expanding circles
    for (int r = 0; r < i; r += 10) {
      display.drawCircle(SCREEN_WIDTH/2, SCREEN_HEIGHT/2, r, SSD1306_WHITE);
    }
    
    // Draw title text that grows in size
    int textSize = map(i, 0, 60, 1, 2);
    display.setTextSize(textSize);
    int16_t x1, y1;
    uint16_t w, h;
    display.getTextBounds("FabLab", 0, 0, &x1, &y1, &w, &h);
    display.setCursor((SCREEN_WIDTH - w) / 2, (SCREEN_HEIGHT - h) / 2);
    display.println("FabLab");
    
    display.display();
    delay(50);
  }
  
  // Final screen
  display.clearDisplay();
  display.setTextSize(2);
  centerText("FabLab", 16);
  display.setTextSize(1);
  centerText("Attendance System", 40);
  display.display();
  delay(1500);
}

void showReadyScreen() {
  static unsigned long lastDisplayUpdate = 0;
  static int blinkState = 0;
  
  if (millis() - lastDisplayUpdate > 500) {
    display.clearDisplay();
    
    // Header
    display.setTextSize(1);
    display.setCursor(0, 0);
    display.println("FabLab Attendance");
    display.drawLine(0, 10, SCREEN_WIDTH, 10, SSD1306_WHITE);
    
    // Card icon
    display.drawBitmap(
      (SCREEN_WIDTH - 32) / 2, 
      14,
      cardIcon, 32, 16, 1);
    
    // Status text
    display.setTextSize(1);
    centerText("Ready to Scan", 36);
    
    // Animated arrow
    if (blinkState) {
      // Draw down arrow
      int baseX = SCREEN_WIDTH / 2;
      int baseY = 50;
      display.fillTriangle(
        baseX, baseY + 8,
        baseX - 8, baseY,
        baseX + 8, baseY,
        SSD1306_WHITE);
    }
    
    // WiFi indicator
    if (WiFi.status() == WL_CONNECTED) {
      for (int i = 0; i < 4; i++) {
        display.drawCircle(8, 54, i*2, SSD1306_WHITE);
      }
    }
    
    display.display();
    blinkState = !blinkState;
    lastDisplayUpdate = millis();
  }
}

void showWiFiConnectingAnimation() {
  if (millis() - lastAnimationUpdate > animationSpeed) {
    animationFrame = (animationFrame + 1) % 4;
    lastAnimationUpdate = millis();
    
    display.clearDisplay();
    display.setTextSize(1);
    centerText("WiFi Connecting", 0);
    
    // Draw WiFi icon
    display.drawBitmap(
      (SCREEN_WIDTH - 32) / 2, 
      16,
      wifiIcon, 32, 16, 1);
    
    // Draw animated dots
    display.setTextSize(2);
    String dots = "";
    for (int i = 0; i < animationFrame; i++) {
      dots += ".";
    }
    centerText(dots, 40);
    
    display.setTextSize(1);
    centerText(ssid, 56);
    
    display.display();
  }
}

void showCardProcessingAnimation() {
  for (int i = 0; i < 8; i++) {
    display.clearDisplay();
    
    // Header
    display.setTextSize(1);
    centerText("Processing Card", 0);
    display.drawLine(0, 10, SCREEN_WIDTH, 10, SSD1306_WHITE);
    
    // Spinning loading indicator
    int centerX = SCREEN_WIDTH / 2;
    int centerY = 32;
    int radius = 15;
    
    for (int j = 0; j < 8; j++) {
      int angle = (j * 45 + i * 45) % 360;
      int x = centerX + radius * cos(angle * PI / 180);
      int y = centerY + radius * sin(angle * PI / 180);
      
      if (j == i) {
        display.fillCircle(x, y, 3, SSD1306_WHITE);
      } else {
        display.drawCircle(x, y, 2, SSD1306_WHITE);
      }
    }
    
    display.setTextSize(1);
    centerText("Please Wait", 50);
    
    display.display();
    delay(100);
  }
}

void showSuccessScreen(String userName) {
  // Zoom-in success animation
  for (int i = 0; i < 16; i++) {
    display.clearDisplay();
    
    int size = i;
    display.drawBitmap(
      (SCREEN_WIDTH - size) / 2, 
      (SCREEN_HEIGHT - size) / 2,
      checkIcon, 16, 16, 1);
      
    display.display();
    delay(20);
  }
  
  delay(300);
  
  // Show success message
  display.clearDisplay();
  
  // Success icon
  display.drawBitmap(
    (SCREEN_WIDTH - 16) / 2, 
    8,
    checkIcon, 16, 16, 1);
  
  // Header
  display.setTextSize(1);
  centerText("Welcome", 28);
  
  // User name
  display.setTextSize(2);
  
  // If name is too long, scroll it
  if (userName.length() > 10) {
    for (int pos = 0; pos < userName.length() - 9; pos++) {
      display.clearDisplay();
      display.drawBitmap((SCREEN_WIDTH - 16) / 2, 8, checkIcon, 16, 16, 1);
      display.setTextSize(1);
      centerText("Welcome", 28);
      display.setTextSize(2);
      centerText(userName.substring(pos, pos + 10), 42);
      display.display();
      delay(300);
    }
  } else {
    centerText(userName, 42);
    display.display();
  }
  
  // Flash borders for successful attendance
  for (int i = 0; i < 3; i++) {
    display.drawRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT, SSD1306_WHITE);
    display.display();
    delay(200);
    display.drawRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT, SSD1306_BLACK);
    display.display();
    delay(200);
  }
}

void showErrorScreen(String cardUid) {
  // Error animation
  for (int i = 0; i < 3; i++) {
    display.clearDisplay();
    
    // Error icon
    display.drawBitmap(
      (SCREEN_WIDTH - 16) / 2, 
      8,
      errorIcon, 16, 16, 1);
      
    display.setTextSize(1);
    centerText("Card Error", 28);
    
    display.setTextSize(1);
    centerText(cardUid, 40);
    
    display.setTextSize(1);
    centerText("Try Again", 52);
    
    display.display();
    delay(500);
    
    display.clearDisplay();
    display.display();
    delay(200);
  }
  
  display.clearDisplay();
  
  // Error icon
  display.drawBitmap(
    (SCREEN_WIDTH - 16) / 2, 
    8,
    errorIcon, 16, 16, 1);
    
  display.setTextSize(1);
  centerText("Card Error", 28);
  
  display.setTextSize(1);
  centerText(cardUid, 40);
  
  display.setTextSize(1);
  centerText("Try Again", 52);
  
  display.display();
}

void connectToWiFi() {
  WiFi.begin(ssid, password);
  
  int timeout = 20;
  while (WiFi.status() != WL_CONNECTED && timeout > 0) {
    showWiFiConnectingAnimation();
    delay(500);
    Serial.print(".");
    timeout--;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println();
    Serial.print("WiFi IP: ");
    Serial.println(WiFi.localIP());
    
    // Connected animation
    display.clearDisplay();
    
    // WiFi icon
    display.drawBitmap(
      (SCREEN_WIDTH - 32) / 2, 
      0,
      wifiIcon, 32, 16, 1);
      
    display.setTextSize(1);
    centerText("Connected", 20);
    
    display.setTextSize(1);
    centerText(WiFi.localIP().toString(), 32);
    
    // Progress bar
    display.drawRect(16, 44, SCREEN_WIDTH - 32, 10, SSD1306_WHITE);
    for (int i = 0; i < SCREEN_WIDTH - 34; i++) {
      display.fillRect(17, 45, i, 8, SSD1306_WHITE);
      display.display();
      delay(10);
    }
    
    display.display();
    delay(1000);
  } else {
    Serial.println("WiFi failed");
    
    display.clearDisplay();
    display.setTextSize(1);
    centerText("WiFi Error", 16);
    centerText("Failed", 32);
    centerText("Retrying...", 48);
    display.display();
    delay(2000);
  }
  
  lastWiFiRetry = millis();
}

String getCardUID() {
  String uidString = "";
  for (byte i = 0; i < mfrc522.uid.size; i++) {
    if (mfrc522.uid.uidByte[i] < 0x10)
      uidString += "0";
    uidString += String(mfrc522.uid.uidByte[i], HEX);
  }
  
  uidString.toUpperCase();
  Serial.print("UID: ");
  Serial.println(uidString);
  
  return uidString;
}

void centerText(String text, int y) {
  int16_t x1, y1;
  uint16_t w, h;
  display.getTextBounds(text, 0, 0, &x1, &y1, &w, &h);
  display.setCursor((SCREEN_WIDTH - w) / 2, y);
  display.print(text);
}

void beepPattern(int pattern) {
  switch (pattern) {
    case 0: // Error pattern
      digitalWrite(BUZZER_PIN, HIGH);
      delay(100);
      digitalWrite(BUZZER_PIN, LOW);
      delay(100);
      digitalWrite(BUZZER_PIN, HIGH);
      delay(100);
      digitalWrite(BUZZER_PIN, LOW);
      break;
    case 1: // Success pattern
      digitalWrite(BUZZER_PIN, HIGH);
      delay(200);
      digitalWrite(BUZZER_PIN, LOW);
      delay(100);
      digitalWrite(BUZZER_PIN, HIGH);
      delay(100);
      digitalWrite(BUZZER_PIN, LOW);
      break;
    default: // Simple beep
      digitalWrite(BUZZER_PIN, HIGH);
      delay(150);
      digitalWrite(BUZZER_PIN, LOW);
      break;
  }
}

void sendAttendanceData(String cardUid) {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("No WiFi");
    showErrorScreen("No WiFi");
    beepPattern(0);
    return;
  }

  if (cardUid == "" || cardUid.length() == 0) {
    Serial.println("Error: Empty UID");
    showErrorScreen("No UID");
    beepPattern(0);
    return;
  }

  Serial.print("UID: ");
  Serial.println(cardUid);

  WiFiClientSecure client;
  client.setInsecure();
  HTTPClient http;

  Serial.print("POST to: ");
  Serial.println(apiUrl);
  http.begin(client, apiUrl);
  http.addHeader("Content-Type", "application/json");
  http.addHeader("Accept", "application/json");
  http.addHeader("Connection", "close");

  StaticJsonDocument<200> jsonDoc;
  jsonDoc["userId"] = cardUid;
  jsonDoc["token"] = apiToken;

  String jsonPayload;
  serializeJson(jsonDoc, jsonPayload);
  Serial.print("Payload: ");
  Serial.println(jsonPayload);

  int httpResponseCode = http.POST(jsonPayload);
  Serial.print("HTTP Code: ");
  Serial.println(httpResponseCode);

  if (httpResponseCode > 0) {
    String response = http.getString();
    Serial.print("Response: ");
    Serial.println(response);

    DynamicJsonDocument respDoc(1024);
    DeserializationError error = deserializeJson(respDoc, response);
    if (!error) {
      bool success = respDoc["success"];
      String message = respDoc["message"];
      if (success) {
        String userName = respDoc["data"]["name"];
        String userId = respDoc["data"]["id"];
        String time = respDoc["data"]["time"];
        Serial.println("Recorded for: " + userName);
        showSuccessScreen(userName);
        beepPattern(1);
      } else {
        showErrorScreen(cardUid);
        beepPattern(0);
      }
    } else {
      Serial.println("Parse Error: " + String(error.c_str()));
      showErrorScreen("Parse Failed");
      beepPattern(0);
    }
  } else {
    Serial.print("HTTP Error: ");
    Serial.println(http.errorToString(httpResponseCode));
    showErrorScreen(String(httpResponseCode));
    beepPattern(0);
  }

  http.end();
}