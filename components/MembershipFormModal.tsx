'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 60 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6, ease: "easeOut" }
  }
};

const scaleIn = {
  hidden: { scale: 0.8, opacity: 0 },
  visible: {
    scale: 1,
    opacity: 1,
    transition: { duration: 0.5, ease: "easeOut" }
  }
};

// Form step animation variants
const formVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.5 }
  },
  exit: {
    opacity: 0,
    x: 20,
    transition: { duration: 0.5 }
  }
};

// Modal animation variants
const modalVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: { duration: 0.3, ease: "easeOut" }
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    transition: { duration: 0.3, ease: "easeIn" }
  }
};

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};

// Form data interface
interface FormData {
  name: string;
  studentId: string;
  department: string;
  email: string;
  mobile: string;
  semester: string;
  domains: string[];
  skills: string;
  weeklyHours: string;
  interests: string[];
  paymentMethod: string;
  senderNumber: string;
  transactionId: string;
  additionalInfo: string;
}

const semesterOptions = [
  'L1T1', 'L1T2', 'L2T1', 'L2T2',
  'L3T1', 'L3T2', 'L4T1', 'L4T2'
];

const domainOptions = [
  { value: 'Design Team (2D & 3D)', icon: '🎨', description: 'Create stunning 2D and 3D designs' },
  { value: 'Printing Team', icon: '🖨️', description: '3D printing and fabrication' },
  { value: 'Machine Operating Team', icon: '⚙️', description: 'Operate advanced machinery' },
  { value: 'R & D Team', icon: '🔬', description: 'Research and development projects' },
  { value: 'Testing Team', icon: '🧪', description: 'Quality testing and validation' },
  { value: 'Event/Social Team', icon: '🎉', description: 'Organize events and workshops' }
];

const interestOptions = [
  { value: '3D Printing', icon: '🖨️', color: 'bg-blue-50 border-blue-200 text-blue-700' },
  { value: 'Laser Cutting', icon: '⚡', color: 'bg-yellow-50 border-yellow-200 text-yellow-700' },
  { value: 'Electronics & IoT', icon: '🔌', color: 'bg-green-50 border-green-200 text-green-700' },
  { value: 'Product Design', icon: '📐', color: 'bg-purple-50 border-purple-200 text-purple-700' },
  { value: 'Research Projects', icon: '🔬', color: 'bg-red-50 border-red-200 text-red-700' },
  { value: 'Workshops & Training', icon: '🎓', color: 'bg-indigo-50 border-indigo-200 text-indigo-700' }
];

const paymentMethods = [
  {
    value: 'bkash',
    label: 'bKash',
    icon: '💳',
    color: 'bg-pink-50 border-pink-200 text-pink-700',
    number: '01712-345678',
    fee: '৳500'
  },
  {
    value: 'nagad',
    label: 'Nagad',
    icon: '📱',
    color: 'bg-orange-50 border-orange-200 text-orange-700',
    number: '01812-345678',
    fee: '৳500'
  },
  {
    value: 'rocket',
    label: 'Rocket',
    icon: '🚀',
    color: 'bg-purple-50 border-purple-200 text-purple-700',
    number: '01912-345678',
    fee: '৳500'
  }
];

// Step names and descriptions
const stepInfo = [
  { name: 'Personal Info', description: 'Tell us about yourself', icon: '👤' },
  { name: 'Contact & Academic', description: 'Your contact and academic details', icon: '📚' },
  { name: 'Skills & Interests', description: 'What excites you most?', icon: '⚡' },
  { name: 'Payment', description: 'Complete your membership', icon: '💳' }
];

interface MembershipFormModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function MembershipFormModal({ isOpen, onClose }: MembershipFormModalProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const { register, handleSubmit, formState: { errors }, reset, watch } = useForm<FormData>();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    try {
      // Export to Excel functionality
      const exportToExcel = () => {
        const csvContent = [
          ['Field', 'Value'],
          ['Name', data.name],
          ['Student ID', data.studentId],
          ['Department', data.department],
          ['Email', data.email],
          ['Mobile', data.mobile],
          ['Semester', data.semester],
          ['Domains', data.domains?.join(', ') || ''],
          ['Skills', data.skills],
          ['Weekly Hours', data.weeklyHours],
          ['Interests', data.interests?.join(', ') || ''],
          ['Payment Method', data.paymentMethod],
          ['Sender Number', data.senderNumber],
          ['Transaction ID', data.transactionId],
          ['Additional Info', data.additionalInfo || '']
        ].map(row => row.join(',')).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `fablab_membership_${data.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      };

      exportToExcel();
      alert('Form submitted successfully! Data has been exported to Excel.');
      reset();
      setCurrentStep(1);
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
      alert('There was an error submitting the form. Please try again.');
    }
    setIsSubmitting(false);
  };

  const nextStep = () => setCurrentStep(prev => Math.min(prev + 1, 4));
  const prevStep = () => setCurrentStep(prev => Math.max(prev - 1, 1));

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
        variants={overlayVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        {/* Backdrop */}
        <motion.div
          className="absolute inset-0 bg-black/60 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Modal */}
        <motion.div
          className="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          <div className="p-8">
            {/* Header */}
            <div className="text-center mb-8">
              <motion.div
                className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4"
                variants={scaleIn}
                initial="hidden"
                animate="visible"
              >
                <span className="text-3xl">🚀</span>
              </motion.div>
              <motion.h2
                className="text-3xl font-bold text-gray-900 mb-2"
                variants={fadeInUp}
                initial="hidden"
                animate="visible"
              >
                Join FabLab DIU
              </motion.h2>
              <motion.p
                className="text-gray-600"
                variants={fadeInUp}
                initial="hidden"
                animate="visible"
                transition={{ delay: 0.1 }}
              >
                Start your innovation journey with us
              </motion.p>
            </div>

        {/* Enhanced Progress Bar */}
        <div className="mb-10">
          <div className="flex justify-between mb-4">
            {stepInfo.map((step, index) => (
              <div
                key={index + 1}
                className={`flex-1 text-center ${
                  currentStep >= index + 1 ? 'text-blue-600' : 'text-gray-400'
                }`}
              >
                <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2 transition-all duration-300 ${
                  currentStep >= index + 1
                    ? 'bg-blue-600 text-white shadow-lg scale-110'
                    : 'bg-gray-200 text-gray-400'
                }`}>
                  <span className="text-lg">{step.icon}</span>
                </div>
                <div className="text-sm font-medium">{step.name}</div>
                <div className="text-xs opacity-75">{step.description}</div>
              </div>
            ))}
          </div>
          <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${(currentStep / 4) * 100}%` }}
            />
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="bg-gradient-to-br from-white to-blue-50/30 rounded-2xl shadow-xl border border-blue-100 p-8">
            {currentStep === 1 && (
              <motion.div
                variants={formVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="space-y-8"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">👤</span>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Personal Information</h2>
                  <p className="text-gray-600">Let&apos;s get to know you better!</p>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <span className="flex items-center">
                        <span className="text-lg mr-2">👤</span>
                        Full Name *
                      </span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        {...register('name', { required: 'Name is required' })}
                        className="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/80 backdrop-blur-sm"
                        placeholder="Enter your full name"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    </div>
                    {errors.name && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-sm text-red-600 flex items-center"
                      >
                        <span className="mr-1">⚠️</span>
                        {errors.name.message}
                      </motion.p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <span className="flex items-center">
                        <span className="text-lg mr-2">🎓</span>
                        Student ID *
                      </span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        {...register('studentId', { required: 'Student ID is required' })}
                        className="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/80 backdrop-blur-sm"
                        placeholder="e.g., ***********"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                        </svg>
                      </div>
                    </div>
                    {errors.studentId && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-sm text-red-600 flex items-center"
                      >
                        <span className="mr-1">⚠️</span>
                        {errors.studentId.message}
                      </motion.p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <span className="flex items-center">
                        <span className="text-lg mr-2">🏫</span>
                        Department *
                      </span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        {...register('department', { required: 'Department is required' })}
                        className="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/80 backdrop-blur-sm"
                        placeholder="e.g., Computer Science & Engineering"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                      </div>
                    </div>
                    {errors.department && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-sm text-red-600 flex items-center"
                      >
                        <span className="mr-1">⚠️</span>
                        {errors.department.message}
                      </motion.p>
                    )}
                  </div>
                </div>
              </motion.div>
            )}

            {currentStep === 2 && (
              <motion.div
                variants={formVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="space-y-8"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">📚</span>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Contact & Academic</h2>
                  <p className="text-gray-600">How can we reach you?</p>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <span className="flex items-center">
                        <span className="text-lg mr-2">📧</span>
                        DIU Email *
                      </span>
                    </label>
                    <div className="relative">
                      <input
                        type="email"
                        {...register('email', {
                          required: 'Email is required',
                          pattern: {
                            value: /@diu\.edu\.bd$/,
                            message: 'Please use your DIU email address'
                          }
                        })}
                        className="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/80 backdrop-blur-sm"
                        placeholder="<EMAIL>"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                        </svg>
                      </div>
                    </div>
                    {errors.email && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-sm text-red-600 flex items-center"
                      >
                        <span className="mr-1">⚠️</span>
                        {errors.email.message}
                      </motion.p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <span className="flex items-center">
                        <span className="text-lg mr-2">📱</span>
                        Mobile Number *
                      </span>
                    </label>
                    <div className="relative">
                      <input
                        type="tel"
                        {...register('mobile', {
                          required: 'Mobile number is required',
                          pattern: {
                            value: /^01[3-9]\d{8}$/,
                            message: 'Please enter a valid Bangladeshi mobile number'
                          }
                        })}
                        className="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/80 backdrop-blur-sm"
                        placeholder="01XXXXXXXXX"
                      />
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                      </div>
                    </div>
                    {errors.mobile && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-sm text-red-600 flex items-center"
                      >
                        <span className="mr-1">⚠️</span>
                        {errors.mobile.message}
                      </motion.p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <span className="flex items-center">
                        <span className="text-lg mr-2">📅</span>
                        Current Semester *
                      </span>
                    </label>
                    <div className="relative">
                      <select
                        {...register('semester', { required: 'Please select your semester' })}
                        className="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/80 backdrop-blur-sm appearance-none"
                      >
                        <option value="">Select your semester</option>
                        {semesterOptions.map((semester) => (
                          <option key={semester} value={semester}>
                            {semester}
                          </option>
                        ))}
                        <option value="other">Other</option>
                      </select>
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                    {errors.semester && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-sm text-red-600 flex items-center"
                      >
                        <span className="mr-1">⚠️</span>
                        {errors.semester.message}
                      </motion.p>
                    )}
                  </div>
                </div>
              </motion.div>
            )}

            {currentStep === 3 && (
              <motion.div
                variants={formVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="space-y-8"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">⚡</span>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Skills & Interests</h2>
                  <p className="text-gray-600">What excites you most?</p>
                </div>

                <div className="space-y-8">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-4">
                      <span className="flex items-center">
                        <span className="text-lg mr-2">🎯</span>
                        Select Your Preferred Teams (Choose 2-3) *
                      </span>
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {domainOptions.map((domain) => (
                        <motion.label
                          key={domain.value}
                          className="group relative cursor-pointer"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <input
                            type="checkbox"
                            value={domain.value}
                            {...register('domains', {
                              required: 'Please select at least 2 teams',
                              validate: (value: string[]) =>
                                (value && value.length >= 2) ||
                                'Please select at least 2 teams'
                            })}
                            className="sr-only peer"
                          />
                          <div className="p-4 border-2 border-gray-200 rounded-xl transition-all duration-200 peer-checked:border-blue-500 peer-checked:bg-blue-50 group-hover:border-blue-300 group-hover:shadow-md">
                            <div className="flex items-start space-x-3">
                              <span className="text-2xl">{domain.icon}</span>
                              <div className="flex-1">
                                <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                                  {domain.value}
                                </h3>
                                <p className="text-sm text-gray-600 mt-1">{domain.description}</p>
                              </div>
                              <div className="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-blue-500 peer-checked:bg-blue-500 transition-all duration-200 flex items-center justify-center">
                                <div className="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100 transition-opacity duration-200"></div>
                              </div>
                            </div>
                          </div>
                        </motion.label>
                      ))}
                    </div>
                    {errors.domains && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-sm text-red-600 flex items-center"
                      >
                        <span className="mr-1">⚠️</span>
                        {errors.domains.message}
                      </motion.p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-4">
                      <span className="flex items-center">
                        <span className="text-lg mr-2">💡</span>
                        Areas of Interest *
                      </span>
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {interestOptions.map((interest) => (
                        <motion.label
                          key={interest.value}
                          className="group cursor-pointer"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <input
                            type="checkbox"
                            value={interest.value}
                            {...register('interests', {
                              required: 'Please select at least one area of interest'
                            })}
                            className="sr-only peer"
                          />
                          <div className={`p-3 border-2 rounded-xl transition-all duration-200 text-center peer-checked:scale-105 peer-checked:shadow-lg ${interest.color} border-transparent peer-checked:border-current`}>
                            <span className="text-2xl block mb-1">{interest.icon}</span>
                            <span className="text-xs font-medium">{interest.value}</span>
                          </div>
                        </motion.label>
                      ))}
                    </div>
                    {errors.interests && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-sm text-red-600 flex items-center"
                      >
                        <span className="mr-1">⚠️</span>
                        {errors.interests.message}
                      </motion.p>
                    )}
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        <span className="flex items-center">
                          <span className="text-lg mr-2">🛠️</span>
                          Your Skills & Experience *
                        </span>
                      </label>
                      <div className="relative">
                        <textarea
                          {...register('skills', { required: 'Please describe your relevant skills' })}
                          rows={4}
                          className="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/80 backdrop-blur-sm resize-none"
                          placeholder="Tell us about your technical skills, programming languages, tools you've used, projects you've worked on..."
                        />
                        <div className="absolute left-4 top-4 text-gray-400">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                          </svg>
                        </div>
                      </div>
                      {errors.skills && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-2 text-sm text-red-600 flex items-center"
                        >
                          <span className="mr-1">⚠️</span>
                          {errors.skills.message}
                        </motion.p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        <span className="flex items-center">
                          <span className="text-lg mr-2">⏰</span>
                          Weekly Time Commitment *
                        </span>
                      </label>
                      <div className="relative">
                        <select
                          {...register('weeklyHours', { required: 'Please select your time commitment' })}
                          className="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/80 backdrop-blur-sm appearance-none"
                        >
                          <option value="">Select hours per week</option>
                          <option value="<5">Less than 5 hours</option>
                          <option value="5-10">5-10 hours</option>
                          <option value="10-15">10-15 hours</option>
                          <option value=">15">More than 15 hours</option>
                        </select>
                        <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                      {errors.weeklyHours && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-2 text-sm text-red-600 flex items-center"
                        >
                          <span className="mr-1">⚠️</span>
                          {errors.weeklyHours.message}
                        </motion.p>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {currentStep === 4 && (
              <motion.div
                variants={formVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="space-y-8"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">💳</span>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Payment</h2>
                  <p className="text-gray-600">Complete your membership registration</p>
                </div>

                <div className="space-y-8">
                  <div className="bg-gradient-to-r from-blue-50 to-green-50 p-6 rounded-xl border border-blue-200">
                    <div className="flex items-center justify-center mb-4">
                      <div className="bg-white p-3 rounded-full shadow-md">
                        <span className="text-2xl">💰</span>
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-center text-gray-900 mb-2">Membership Fee</h3>
                    <p className="text-3xl font-bold text-center text-green-600 mb-2">৳500</p>
                    <p className="text-sm text-center text-gray-600">One-time registration fee</p>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-4">
                      <span className="flex items-center">
                        <span className="text-lg mr-2">💳</span>
                        Select Payment Method *
                      </span>
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {paymentMethods.map((method) => (
                        <motion.label
                          key={method.value}
                          className="group cursor-pointer"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <input
                            type="radio"
                            value={method.value}
                            {...register('paymentMethod', { required: 'Please select a payment method' })}
                            className="sr-only peer"
                          />
                          <div className={`p-4 border-2 rounded-xl transition-all duration-200 peer-checked:border-blue-500 peer-checked:shadow-lg group-hover:shadow-md ${method.color} border-transparent peer-checked:border-current`}>
                            <div className="text-center">
                              <span className="text-3xl block mb-2">{method.icon}</span>
                              <h3 className="font-bold text-lg">{method.label}</h3>
                              <p className="text-sm opacity-75">{method.fee}</p>
                            </div>
                          </div>
                        </motion.label>
                      ))}
                    </div>
                    {errors.paymentMethod && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-sm text-red-600 flex items-center"
                      >
                        <span className="mr-1">⚠️</span>
                        {errors.paymentMethod.message}
                      </motion.p>
                    )}
                  </div>

                  {watch('paymentMethod') && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="bg-blue-50 p-6 rounded-xl border border-blue-200"
                    >
                      <div className="text-center mb-6">
                        <div className="bg-blue-100 p-3 rounded-full inline-block mb-3">
                          <span className="text-2xl">📱</span>
                        </div>
                        <h3 className="text-lg font-bold text-gray-900 mb-2">Payment Instructions</h3>
                        <p className="text-gray-600">Send ৳500 to the number below and fill in the details</p>
                      </div>

                      {paymentMethods.map((method) =>
                        watch('paymentMethod') === method.value && (
                          <motion.div
                            key={method.value}
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            className="bg-white p-4 rounded-lg border border-blue-200 mb-6"
                          >
                            <div className="flex items-center justify-center mb-3">
                              <span className="text-2xl mr-2">{method.icon}</span>
                              <span className="font-bold text-lg">{method.label}</span>
                            </div>
                            <div className="text-center">
                              <p className="text-sm text-gray-600 mb-1">Send Money To:</p>
                              <p className="text-2xl font-bold text-blue-600 mb-2">{method.number}</p>
                              <p className="text-sm text-gray-600">Amount: <span className="font-bold text-green-600">{method.fee}</span></p>
                            </div>
                          </motion.div>
                        )
                      )}

                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">
                            <span className="flex items-center">
                              <span className="text-lg mr-2">📱</span>
                              Your Number (From which you sent money) *
                            </span>
                          </label>
                          <div className="relative">
                            <input
                              type="tel"
                              {...register('senderNumber', {
                                required: 'Please enter your number',
                                pattern: {
                                  value: /^01[3-9]\d{8}$/,
                                  message: 'Please enter a valid Bangladeshi mobile number'
                                }
                              })}
                              className="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/80 backdrop-blur-sm"
                              placeholder="01XXXXXXXXX"
                            />
                            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                              </svg>
                            </div>
                          </div>
                          {errors.senderNumber && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-red-600 flex items-center"
                            >
                              <span className="mr-1">⚠️</span>
                              {errors.senderNumber.message}
                            </motion.p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">
                            <span className="flex items-center">
                              <span className="text-lg mr-2">🔢</span>
                              Transaction ID *
                            </span>
                          </label>
                          <div className="relative">
                            <input
                              type="text"
                              {...register('transactionId', { required: 'Please enter transaction ID' })}
                              className="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/80 backdrop-blur-sm"
                              placeholder="Enter transaction ID"
                            />
                            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            </div>
                          </div>
                          {errors.transactionId && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-red-600 flex items-center"
                            >
                              <span className="mr-1">⚠️</span>
                              {errors.transactionId.message}
                            </motion.p>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  )}

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      <span className="flex items-center">
                        <span className="text-lg mr-2">💬</span>
                        Additional Comments (Optional)
                      </span>
                    </label>
                    <div className="relative">
                      <textarea
                        {...register('additionalInfo')}
                        rows={3}
                        className="w-full px-4 py-3 pl-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/80 backdrop-blur-sm resize-none"
                        placeholder="Any additional information you'd like to share..."
                      />
                      <div className="absolute left-4 top-4 text-gray-400">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Enhanced Navigation Buttons */}
            <div className="flex justify-between items-center mt-10 pt-6 border-t border-gray-200">
              {currentStep > 1 ? (
                <motion.button
                  type="button"
                  onClick={prevStep}
                  className="group flex items-center px-6 py-3 border-2 border-gray-300 rounded-xl text-gray-700 hover:border-blue-500 hover:text-blue-600 transition-all duration-200 font-medium"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <svg className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Previous
                </motion.button>
              ) : (
                <div></div>
              )}

              <div className="flex items-center space-x-2">
                {[1, 2, 3, 4].map((step) => (
                  <div
                    key={step}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      currentStep >= step ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>

              {currentStep < 4 ? (
                <motion.button
                  type="button"
                  onClick={nextStep}
                  className="group flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-medium shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Next
                  <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </motion.button>
              ) : (
                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  className={`group flex items-center px-8 py-3 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-200 font-medium shadow-lg hover:shadow-xl ${
                    isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  whileHover={!isSubmitting ? { scale: 1.02 } : {}}
                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : (
                    <>
                      Complete Registration
                      <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </>
                  )}
                </motion.button>
              )}
            </div>
          </div>
        </form>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}