# Firebase Admin SDK Setup Guide

This guide provides instructions on setting up Firebase Admin SDK for this project.

## Getting Firebase Service Account Credentials

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project (or create a new one if needed)
3. Click on the gear icon ⚙️ next to "Project Overview" to access Project Settings
4. Navigate to the "Service accounts" tab
5. Click on "Generate new private key" button
6. Save the downloaded JSON file securely (do NOT commit it to version control)

## Setting Up Environment Variables

1. Create a `.env.local` file in the root of the project
2. Add the following variables, using values from your downloaded JSON file:

```
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nXXXX...XXXX\n-----END PRIVATE KEY-----\n"
API_TOKEN=your_api_token_here
```

Important notes:
- Make sure to put the entire private key in quotes
- Maintain the `\n` characters in the private key
- Never commit `.env.local` to version control

## Verifying Your Setup

Run the following command to verify your Firebase credentials are set up correctly:

```bash
npm run check-firebase
```

This will check if all required environment variables are present and properly formatted.

## Troubleshooting

### Error: "Could not load the default credentials"

This error occurs when Firebase Admin SDK cannot find valid credentials. Check that:

1. You've created a `.env.local` file with the correct credentials
2. The private key is properly formatted with newline characters (`\n`)
3. Your service account has the necessary permissions

### Error: "The "payload" argument must be of type object. Received null"

This typically indicates an issue with the credential format. Make sure:

1. Your private key is wrapped in double quotes
2. All newline characters are properly escaped as `\n`
3. The entire private key is included, starting with `-----BEGIN PRIVATE KEY-----` and ending with `-----END PRIVATE KEY-----`

### Other Issues

If you're still having problems:

1. Double-check that the service account credentials are for the correct Firebase project
2. Ensure the Firebase project has Firestore enabled
3. Verify that your service account has the necessary permissions 